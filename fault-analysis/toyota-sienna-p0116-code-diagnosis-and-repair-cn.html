<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html lang="zh-CN">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title>2000年丰田Sienna P0116故障诊断与修复 | GeekOBD</title>
    <meta name="description" content="通过专家指导诊断和修复2000年丰田Sienna的P0116故障码问题，了解冷却液温度传感器的问题。">
    <meta name="keywords" content="丰田Sienna, P0116, 冷却液温度传感器">
    <meta name="author" content="GeekOBD Automotive Experts">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <link rel="canonical" href="https://www.geekobd.com/fault-analysis/toyota-sienna-p0116-code-diagnosis-and-repair.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:title" content="2000年丰田Sienna P0116故障诊断与修复">
    <meta property="og:description" content="通过专家指导诊断和修复2000年丰田Sienna的P0116故障码问题，了解冷却液温度传感器的问题。">
    <meta property="og:url" content="https://www.geekobd.com/fault-analysis/toyota-sienna-p0116-code-diagnosis-and-repair.html">
    <meta property="og:image" content="https://www.geekobd.com/img/logo.png">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-RD6767XBCL');
    </script>

    <style>
        /* 与DTC页面一致的样式 */
        .main-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        /* 快速回答区域 */
        .quick-answer {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 25px;
            margin: 30px 0;
            border-radius: 5px;
        }

        /* 问题概述样式 */
        .problem-overview {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* 原因列表样式 - 使用有序列表 */
        .causes-list ol {
            counter-reset: cause-counter;
            list-style: none;
            padding-left: 0;
        }

        .causes-list li {
            counter-increment: cause-counter;
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #667eea;
            border-radius: 5px;
            position: relative;
        }

        .causes-list li::before {
            content: counter(cause-counter) ".";
            font-weight: bold;
            color: #667eea;
            margin-right: 10px;
        }

        /* 诊断步骤样式 */
        .diagnostic-steps ol {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }

        .diagnostic-steps li {
            counter-increment: step-counter;
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #17a2b8;
            border-radius: 5px;
        }

        .diagnostic-steps li::before {
            content: "Step " counter(step-counter) ": ";
            font-weight: bold;
            color: #17a2b8;
            display: block;
            margin-bottom: 10px;
        }

        /* 维修建议样式 */
        .repair-recommendations .recommendation-item {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #ffc107;
            border-radius: 5px;
        }

        .priority {
            background: #fff3e0;
            color: #f57c00;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        /* 预防提示样式 */
        .preventive-tips ul {
            list-style: none;
            padding-left: 0;
        }

        .preventive-tips li {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #28a745;
            border-radius: 5px;
            position: relative;
        }

        .preventive-tips li::before {
            content: "💡";
            margin-right: 10px;
        }

        /* 车辆信息样式 */
        .vehicle-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .vehicle-info span {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            margin: 2px;
            font-size: 12px;
        }

        /* 标题样式 */
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
        .breadcrumb-custom {
            background: none;
            padding: 20px 0;
            margin: 0;
        }
        .breadcrumb-custom a {
            color: #667eea;
            text-decoration: none;
        }
        .breadcrumb-custom a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .fault-analysis-header {
                padding: 30px 0 20px;
            }
            .fault-code-badge {
                font-size: 18px;
                padding: 6px 14px;
            }
            .vehicle-info-badge {
                display: block;
                margin: 5px 0;
                width: fit-content;
            }
            .content-section {
                padding: 20px 15px;
                margin-bottom: 15px;
            }
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "丰田Sienna P0116故障诊断与修复",
        "description": "2000年丰田Sienna搭载3.0L V6发动机，出现了P0116故障码，表示冷却液温度传感器电路范围/性能问题。该车大约行驶119,000英里，可能受到环境因素的影响，例如传感器的腐蚀或磨损。冷却液温度传感器位于恒温器外壳附近，可以通过跟踪上部散热器管来访问。电路包括来自发动机控制模块（ECM）的5V参考电源，信号线的电压应在0.5V至4.5V之间变化，具体取决于冷却液温度。在60摄氏度时，通常应读取2-3V。如果读数显著偏离或为0V，则表明参考电路可能断开或传感器的连接不良。警告灯表示发动机性能可能存在问题，因为PCM依赖于准确的温度读数来管理燃油和提高发动机效率。鉴于车辆当前的运行状态，诊断传感器的完整性及相关线路至关重要，以确保发动机管理系统正常工作。",
        "author": {
            "@type": "Organization",
            "name": "GeekOBD"
        },
        "publisher": {
            "@type": "Organization",
            "name": "GeekOBD"
        },
        "datePublished": "2025-08-05T21:38:18.900535",
        "dateModified": "2025-08-05T21:38:18.900535",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://www.geekobd.com/fault-analysis/toyota-sienna-p0116-code-diagnosis-and-repair.html"
        }
    }
    </script>
</head>

<body class="page">
    <div class="wrap">
        <!-- Header Start -->
        <header id="header" role="banner">
            <!-- Main Header Start -->
            <div class="main-header">
                <div class="container">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="logo pull-left">
                                <h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <!-- Mobile Menu Start -->
                            <div class="mobile navbar-header">
                                <a class="navbar-toggle" data-toggle="collapse" data-target=".menu">
                                    <i class="icon-reorder icon-2x"></i>
                                </a>
                            </div>
                            <!-- Mobile Menu End -->
                            <!-- Menu Start -->
                            <nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
                                <ul class="nav navbar-nav sf-menu">
                                    <li><a href="../index.html" class="sf-with-ul">首页</a></li>
                                    <li><a href="../app.html" class="sf-with-ul">APP</a></li>
                                    <li><a href="javascript:;;" class="sf-with-ul">适配器 <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
                                        <ul>
                                            <li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
                                            <li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="javascript:;;" class="sf-with-ul">资源 <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
                                        <ul>
                                            <li><a href="../dtc-codes.html" class="sf-with-ul">DTC代码</a></li>
                                            <li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD指南</a></li>
                                            <li><a href="../vehicle-compatibility.html" class="sf-with-ul">兼容性</a></li>
                                            <li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">燃油效率</a></li>
                                            <li><a href="../support.html" class="sf-with-ul">支持</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="../about.html" class="sf-with-ul">关于我们</a></li>
                                    <li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
                                </ul>
                            </nav>
                            <!-- Menu End -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Header End -->
        </header>
        <!-- Header End -->

        <!-- Breadcrumb -->
        <div class="container">
            <nav class="breadcrumb-custom">
                <a href="../index.html">首页</a> &raquo;
                <a href="../fault-analysis.html">故障分析</a> &raquo;
                <span>丰田Sienna P0116故障诊断与修复</span>
            </nav>
        </div>

        <!-- Content Start -->
        <main id="main" role="main">
            <!-- Title, Breadcrumb Start-->
            <section class="breadcrumb-wrapper">
                <div class="container" style="min-height:86px">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
                            <h1 class="title">丰田Sienna P0116故障诊断与修复</h1>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Title, Breadcrumb End-->

            <div class="container" style="padding: 30px 15px;">
                <!-- Vehicle Info -->
                <div class="vehicle-info">
                    <strong>Vehicle:</strong>
                    <span>Toyota</span>
                    <span>Sienna</span>
                    <span>2000</span>
                </div>

                <div class="row">
                    <!-- Main Content Column -->
                    <div class="col-lg-8 col-md-8">
                        <!-- User Problem Summary Section -->
                        <div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); border: 2px solid #ff9800; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                            <h2 id="user-problem-summary" style="color: #f57c00; margin-bottom: 20px; font-size: 24px;"><i class="fa fa-user"></i> 车主问题描述</h2>
                            <div style="font-size: 16px; color: #333; line-height: 1.6;">
                                车主报告其2000年丰田Sienna行驶119,000英里时出现了P0116故障码。该故障码表示冷却液温度传感器电路范围/性能问题。车主提到尽管有警告灯，车辆仍能正常行驶。他们希望了解该故障码的含义、影响，以及如何定位和修复问题。
                            </div>
                        </div>

                        <!-- Problem Overview Section -->
                        <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 2px solid #2196f3; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                            <h2 id="problem-overview" style="color: #1976d2; margin-bottom: 20px; font-size: 24px;"><i class="fa fa-cogs"></i> 专业技术分析</h2>
                            <div style="font-size: 16px; color: #333; line-height: 1.6;">
                                2000年丰田Sienna搭载3.0L V6发动机，出现了P0116故障码，表示冷却液温度传感器电路范围/性能问题。该车大约行驶119,000英里，可能受到环境因素的影响，例如传感器的腐蚀或磨损。冷却液温度传感器位于恒温器外壳附近，可以通过跟踪上部散热器管来访问。电路包括来自发动机控制模块（ECM）的5V参考电源，信号线的电压应在0.5V至4.5V之间变化，具体取决于冷却液温度。在60摄氏度时，通常应读取2-3V。如果读数显著偏离或为0V，则表明参考电路可能断开或传感器的连接不良。警告灯表示发动机性能可能存在问题，因为PCM依赖于准确的温度读数来管理燃油和提高发动机效率。鉴于车辆当前的运行状态，诊断传感器的完整性及相关线路至关重要，以确保发动机管理系统正常工作。
                            </div>
                        </div>

                        <!-- Possible Causes -->
                        <div class="main-content">
                            <h2 id="causes"><i class="fa fa-search"></i> 可能原因</h2>
                            <p style="margin-bottom: 15px; color: #666; font-size: 14px;">最常见原因（按频率排序）:</p>
                            <div class="causes-list">
                                <ol>
                                    <li>冷却液温度传感器故障：传感器可能发生故障，向PCM提供不正确的电压读数。这可能是由于传感器内部故障或外部环境因素（如腐蚀或损坏）造成的。正常情况下，读数应根据冷却液温度变化，从0.5V到4.5V不等，60摄氏度时应在2-3V之间。如果传感器卡住或失效，可能会输出静态电压，通常约为0V。 - 发动机故障灯亮，电压读数超出预期范围，可能在连接器处发现腐蚀的可视检查。</li>
<li>线路问题：通向冷却液温度传感器的线束可能损坏或磨损，影响发送到PCM的信号。检查线路连续性，检查是否有短路或断路至关重要。如果线路受损，ECM的参考电压可能无法到达传感器。 - 可视检查发现传感器附近有磨损的电线，实时数据监控中信号间歇性丢失。</li>
<li>ECM故障：发动机控制模块本身可能故障，无法正确处理来自冷却液温度传感器的信号。这种情况较少见，但可能由于软件问题或ECM内部故障而发生。 - 存储的故障码指示ECM问题，所有相关传感器读数似乎不稳定。</li>

                                </ol>
                            </div>
                        </div>

                        <!-- Diagnostic Steps -->
                        <div class="main-content diagnostic-steps">
                            <h2 id="diagnostic-steps"><i class="fa fa-stethoscope"></i> 诊断步骤</h2>
                            <h3>专业诊断流程</h3>
                            <p style="margin-bottom: 15px; color: #666;">按照这些系统性步骤准确诊断问题。每个步骤都建立在前一个步骤的基础上，确保准确诊断。</p>
                            <ol>
                                <li>第一步 - OBD-II故障码读取：首先使用专业的OBD-II扫描仪或GeekOBD APP读取故障码。在获取P0116故障码后，检查是否有其他可能指示相关问题的故障码。查看冻结帧数据，了解设置故障码时的条件。</li>
<li>第二步 - 目视检查：对冷却液温度传感器及其线路进行彻底的目视检查。查看是否有任何物理损坏、腐蚀或连接松动的迹象。确保传感器连接器牢固连接，且没有磨损的电线通向传感器。</li>
<li>第三步 - 电压测试：使用数字万用表测量冷却液温度传感器连接器的电压。检查参考电压（应约为5V）和信号电压（应在0.5V到4.5V之间变化）。如果读数不在这些范围内，需进一步诊断线路和传感器的完整性。</li>
<li>第四步 - 实时数据监控：在发动机运行时，使用GeekOBD APP或专业扫描仪监控冷却液温度传感器的实时数据。观察发动机升温时的电压输出，确保其根据温度变化而波动。如果保持静态或不适当地响应，这表明传感器故障或线路问题。</li>

                            </ol>
                        </div>

                        <!-- Repair Recommendations -->
                        <div class="main-content repair-recommendations">
                            <h2 id="repair-recommendations"><i class="fa fa-wrench"></i> 维修建议</h2>
                            <div class="recommendations-list">
                                
            <div class="recommendation-item priority-critical">
                <p>紧急优先 - 更换故障冷却液温度传感器：如果诊断为故障，使用原厂零件（丰田零件号89422-33030，通常价格在300-700元）更换冷却液温度传感器。确保正确安装和连接牢固。更换后，使用GeekOBD APP清除故障码，并在试车时验证正常运行。</p>
                <span class="priority">优先级: critical</span>
            </div>
            
            <div class="recommendation-item priority-high">
                <p>重点维修 - 修复线路问题：如果发现线路损坏，修复或更换受影响的线束。使用热缩管和焊接连接以确保耐用性。修复后，重新测试传感器电压并监控实时数据，以确保正常功能。</p>
                <span class="priority">优先级: high</span>
            </div>
            
            <div class="recommendation-item priority-medium">
                <p>预防保养 - 检查ECM和软件更新：如果传感器或线路没有发现故障，考虑检查ECM是否需要软件更新或存在故障。如有需要，请咨询经销商进行重新编程或更换。 </p>
                <span class="priority">优先级: medium</span>
            </div>
            
                            </div>
                        </div>

                        <!-- Preventive Tips -->
                        <div class="main-content preventive-tips">
                            <h2 id="preventive-tips"><i class="fa fa-shield"></i> 预防提示</h2>
                            <ul>
                                <li>定期传感器测试：将冷却液温度传感器测试纳入车辆维护计划。定期使用GeekOBD APP检查故障码和监控性能参数，以便在问题升级之前及时发现。</li>
<li>连接器维护：定期检查和清洁与冷却液温度传感器相关的连接器，以防止腐蚀并确保可靠连接。涂抹绝缘脂以防止潮气侵入。</li>
<li>定期冷却系统冲洗：根据制造商推荐的间隔进行冷却系统冲洗，以保持冷却系统清洁和正常运作，这有助于延长传感器的使用寿命。</li>

                            </ul>
                        </div>
                    </div>

                    <!-- Sidebar Column -->
                    <div class="col-lg-4 col-md-4">
                        <!-- GeekOBD APP Promotion -->
                        <div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: white; font-size: 20px; text-align: center;"><i class="fa fa-mobile"></i> 诊断问题</h4>
                            <p style="margin-bottom: 15px; color: rgba(255,255,255,0.9); font-size: 14px; text-align: center;">使用GeekOBD APP进行专业诊断！</p>
                            <div style="margin-bottom: 20px;">
                                <ul style="color: rgba(255,255,255,0.9); font-size: 14px; margin: 0; padding-left: 0; list-style: none;">
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>实时数据监控</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>高级诊断功能</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>逐步维修指导</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>专业级分析</li>
                                </ul>
                            </div>
                            <div class="button-container" style="display: flex; gap: 10px; flex-direction: column;">
                                <a href="../app.html" style="background: rgba(255,255,255,0.2); color: white; padding: 15px 20px; text-decoration: none; border-radius: 25px; text-align: center; font-size: 16px; font-weight: 600; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s;">
                                    下载GeekOBD APP
                                </a>
                                <a href="../hardware.html" style="background: rgba(255,255,255,0.2); color: white; padding: 15px 20px; text-decoration: none; border-radius: 25px; text-align: center; font-size: 16px; font-weight: 600; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s;">
                                    获取MOBD适配器
                                </a>
                            </div>
                        </div>

                        <!-- Quick Navigation -->
                        <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-compass"></i> 快速导航</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <a href="#user-problem-summary" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-user"></i> 车主问题描述
                                </a>
                                <a href="#problem-overview" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-cogs"></i> 专业技术分析
                                </a>
                                <a href="#causes" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-search"></i> 可能原因
                                </a>
                                <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-stethoscope"></i> 诊断步骤
                                </a>
                                <a href="#repair-recommendations" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-wrench"></i> 维修建议
                                </a>
                                <a href="#preventive-tips" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-shield"></i> 预防提示
                                </a>
                            </div>
                        </div>

                        <!-- Diagnostic Resources -->
                        <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-book"></i> 诊断资源</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <a href="../obd-diagnostic-guide.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    OBD诊断指南
                                </a>
                                <a href="../vehicle-compatibility.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    车辆兼容性
                                </a>
                                <a href="../fuel-efficiency-monitoring.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    燃油效率提示
                                </a>
                                <a href="../support.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    技术支持
                                </a>
                                <a href="../blog.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    最新文章
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </main>
        <!-- Content End -->
    </div>

    <!-- JavaScript -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.js"></script>
    <script src="../js/superfish.js"></script>
    <script src="../js/custom.js"></script>
</body>
</html>