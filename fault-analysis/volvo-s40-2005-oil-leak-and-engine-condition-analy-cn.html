<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html lang="zh-CN">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title>2005年沃尔沃S40漏油诊断与维修建议 | GeekOBD</title>
    <meta name="description" content="2005年沃尔沃S40漏油问题的详细分析，包括可能原因和维修建议。">
    <meta name="keywords" content="沃尔沃S40, 漏油, 发动机维修">
    <meta name="author" content="GeekOBD Automotive Experts">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <link rel="canonical" href="https://www.geekobd.com/fault-analysis/volvo-s40-2005-oil-leak-and-engine-condition-analy.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:title" content="2005年沃尔沃S40漏油诊断与维修建议">
    <meta property="og:description" content="2005年沃尔沃S40漏油问题的详细分析，包括可能原因和维修建议。">
    <meta property="og:url" content="https://www.geekobd.com/fault-analysis/volvo-s40-2005-oil-leak-and-engine-condition-analy.html">
    <meta property="og:image" content="https://www.geekobd.com/img/logo.png">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-RD6767XBCL');
    </script>

    <style>
        /* 与DTC页面一致的样式 */
        .main-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        /* 快速回答区域 */
        .quick-answer {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 25px;
            margin: 30px 0;
            border-radius: 5px;
        }

        /* 问题概述样式 */
        .problem-overview {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* 原因列表样式 - 使用有序列表 */
        .causes-list ol {
            counter-reset: cause-counter;
            list-style: none;
            padding-left: 0;
        }

        .causes-list li {
            counter-increment: cause-counter;
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #667eea;
            border-radius: 5px;
            position: relative;
        }

        .causes-list li::before {
            content: counter(cause-counter) ".";
            font-weight: bold;
            color: #667eea;
            margin-right: 10px;
        }

        /* 诊断步骤样式 */
        .diagnostic-steps ol {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }

        .diagnostic-steps li {
            counter-increment: step-counter;
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #17a2b8;
            border-radius: 5px;
        }

        .diagnostic-steps li::before {
            content: "Step " counter(step-counter) ": ";
            font-weight: bold;
            color: #17a2b8;
            display: block;
            margin-bottom: 10px;
        }

        /* 维修建议样式 */
        .repair-recommendations .recommendation-item {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #ffc107;
            border-radius: 5px;
        }

        .priority {
            background: #fff3e0;
            color: #f57c00;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        /* 预防提示样式 */
        .preventive-tips ul {
            list-style: none;
            padding-left: 0;
        }

        .preventive-tips li {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-left: 4px solid #28a745;
            border-radius: 5px;
            position: relative;
        }

        .preventive-tips li::before {
            content: "💡";
            margin-right: 10px;
        }

        /* 车辆信息样式 */
        .vehicle-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .vehicle-info span {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            margin: 2px;
            font-size: 12px;
        }

        /* 标题样式 */
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 20px;
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
        .breadcrumb-custom {
            background: none;
            padding: 20px 0;
            margin: 0;
        }
        .breadcrumb-custom a {
            color: #667eea;
            text-decoration: none;
        }
        .breadcrumb-custom a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .fault-analysis-header {
                padding: 30px 0 20px;
            }
            .fault-code-badge {
                font-size: 18px;
                padding: 6px 14px;
            }
            .vehicle-info-badge {
                display: block;
                margin: 5px 0;
                width: fit-content;
            }
            .content-section {
                padding: 20px 15px;
                margin-bottom: 15px;
            }
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "2005年沃尔沃S40油漏和发动机状况分析",
        "description": "这辆2005年沃尔沃S40搭载2.5L I5发动机，预计行驶里程超过12万公里。主要症状是油底壳漏油，可能是由于老化或腐蚀导致油底壳垫圈出现问题。油尺上显示的低油位表明漏油情况严重。一位机械师建议更换油底壳，这是这种年份车辆常见的故障，特别是在冬季气候中暴露于路盐的环境因素下。没有敲击声是个积极信号，表明发动机可能还没有受到内部损伤，但长时间低油位可能导致严重的发动机故障。必须仔细检查油底壳是否有裂缝，并评估机油滤清器和放油螺母的状况。油压应检查，正常读数在怠速和负载下应在25-60 PSI之间。考虑到车辆的年龄和里程，检查机油的状况也很重要，如果机油被污染则应更换。车主需要意识到，如果忽视此项维修，可能会导致更严重的发动机问题，包括油位过低导致发动机卡死的风险。",
        "author": {
            "@type": "Organization",
            "name": "GeekOBD"
        },
        "publisher": {
            "@type": "Organization",
            "name": "GeekOBD"
        },
        "datePublished": "2025-08-05T18:09:09.097736",
        "dateModified": "2025-08-05T18:09:09.097736",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://www.geekobd.com/fault-analysis/volvo-s40-2005-oil-leak-and-engine-condition-analy.html"
        }
    }
    </script>
</head>

<body class="page">
    <div class="wrap">
        <!-- Header Start -->
        <header id="header" role="banner">
            <!-- Main Header Start -->
            <div class="main-header">
                <div class="container">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="logo pull-left">
                                <h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <!-- Mobile Menu Start -->
                            <div class="mobile navbar-header">
                                <a class="navbar-toggle" data-toggle="collapse" data-target=".menu">
                                    <i class="icon-reorder icon-2x"></i>
                                </a>
                            </div>
                            <!-- Mobile Menu End -->
                            <!-- Menu Start -->
                            <nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
                                <ul class="nav navbar-nav sf-menu">
                                    <li><a href="../index.html" class="sf-with-ul">首页</a></li>
                                    <li><a href="../app.html" class="sf-with-ul">APP</a></li>
                                    <li><a href="javascript:;;" class="sf-with-ul">适配器 <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
                                        <ul>
                                            <li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
                                            <li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="javascript:;;" class="sf-with-ul">资源 <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
                                        <ul>
                                            <li><a href="../dtc-codes.html" class="sf-with-ul">DTC代码</a></li>
                                            <li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD指南</a></li>
                                            <li><a href="../vehicle-compatibility.html" class="sf-with-ul">兼容性</a></li>
                                            <li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">燃油效率</a></li>
                                            <li><a href="../support.html" class="sf-with-ul">支持</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="../about.html" class="sf-with-ul">关于我们</a></li>
                                    <li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
                                </ul>
                            </nav>
                            <!-- Menu End -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Header End -->
        </header>
        <!-- Header End -->

        <!-- Breadcrumb -->
        <div class="container">
            <nav class="breadcrumb-custom">
                <a href="../index.html">首页</a> &raquo;
                <a href="../fault-analysis.html">故障分析</a> &raquo;
                <span>2005年沃尔沃S40油漏和发动机状况分析</span>
            </nav>
        </div>

        <!-- Content Start -->
        <main id="main" role="main">
            <!-- Title, Breadcrumb Start-->
            <section class="breadcrumb-wrapper">
                <div class="container" style="min-height:86px">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
                            <h1 class="title">2005年沃尔沃S40油漏和发动机状况分析</h1>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Title, Breadcrumb End-->

            <div class="container" style="padding: 30px 15px;">
                <!-- Vehicle Info -->
                <div class="vehicle-info">
                    <strong>Vehicle:</strong>
                    <span>Volvo</span>
                    <span>S40</span>
                    <span>2005</span>
                </div>

                <div class="row">
                    <!-- Main Content Column -->
                    <div class="col-lg-8 col-md-8">
                        <!-- User Problem Summary Section -->
                        <div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); border: 2px solid #ff9800; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                            <h2 id="user-problem-summary" style="color: #f57c00; margin-bottom: 20px; font-size: 24px;"><i class="fa fa-user"></i> 车主问题描述</h2>
                            <div style="font-size: 16px; color: #333; line-height: 1.6;">
                                车主反映他们的2005年沃尔沃S40油底壳有漏油现象。之前车辆使用合成机油，车主认为尽管油尺显示油位低，但仍有油在漏。发动机没有发出敲击声，车主的妻子也没有听到任何表明发动机运转不良的噪音。一位机械师建议需要更换油底壳。车主对漏油的影响以及如果发动机出现故障是否需要交易车辆感到担忧。
                            </div>
                        </div>

                        <!-- Problem Overview Section -->
                        <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 2px solid #2196f3; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                            <h2 id="problem-overview" style="color: #1976d2; margin-bottom: 20px; font-size: 24px;"><i class="fa fa-cogs"></i> 专业技术分析</h2>
                            <div style="font-size: 16px; color: #333; line-height: 1.6;">
                                这辆2005年沃尔沃S40搭载2.5L I5发动机，预计行驶里程超过12万公里。主要症状是油底壳漏油，可能是由于老化或腐蚀导致油底壳垫圈出现问题。油尺上显示的低油位表明漏油情况严重。一位机械师建议更换油底壳，这是这种年份车辆常见的故障，特别是在冬季气候中暴露于路盐的环境因素下。没有敲击声是个积极信号，表明发动机可能还没有受到内部损伤，但长时间低油位可能导致严重的发动机故障。必须仔细检查油底壳是否有裂缝，并评估机油滤清器和放油螺母的状况。油压应检查，正常读数在怠速和负载下应在25-60 PSI之间。考虑到车辆的年龄和里程，检查机油的状况也很重要，如果机油被污染则应更换。车主需要意识到，如果忽视此项维修，可能会导致更严重的发动机问题，包括油位过低导致发动机卡死的风险。
                            </div>
                        </div>

                        <!-- Possible Causes -->
                        <div class="main-content">
                            <h2 id="causes"><i class="fa fa-search"></i> 可能原因</h2>
                            <p style="margin-bottom: 15px; color: #666; font-size: 14px;">最常见原因（按频率排序）:</p>
                            <div class="causes-list">
                                <ol>
                                    <li>油底壳垫圈漏油：老旧车辆常见故障，因材料老化导致漏油。油底壳垫圈通常需要在75000-100000公里更换。需要彻底检查油底壳及周围区域以确认漏油源。 - 车辆底部油渍，油尺上油位低，机械师建议更换。</li>
<li>油底壳裂纹：油底壳的物理损伤可能导致大量漏油。这在经历过碰撞或腐蚀的车辆中尤其常见。应进行视觉检查，如有必要可进行压力测试以确认裂纹。 - 油底壳上可见裂纹或变形，漏油严重，压力测试结果。</li>
<li>放油螺母磨损：随着时间推移，放油螺母可能磨损，导致漏油。这常常被忽视，但在更换机油时应定期检查。 - 放油螺母区域漏油，螺母松动或损坏，需要更换。</li>
<li>机油污染：如果机油没有定期更换，可能会被污染，导致内部部件损坏和漏油。定期更换机油对发动机健康至关重要。 - 更换时机油颜色变暗或粗糙，可能的内部损伤迹象，服务记录显示更换间隔过长。</li>

                                </ol>
                            </div>
                        </div>

                        <!-- Diagnostic Steps -->
                        <div class="main-content diagnostic-steps">
                            <h2 id="diagnostic-steps"><i class="fa fa-stethoscope"></i> 诊断步骤</h2>
                            <h3>专业诊断流程</h3>
                            <p style="margin-bottom: 15px; color: #666;">按照这些系统性步骤准确诊断问题。每个步骤都建立在前一个步骤的基础上，确保准确诊断。</p>
                            <ol>
                                <li>第一步 - 视觉检查：首先彻底检查油底壳、放油螺母及周围区域，寻找漏油或物理损伤的迹象。这包括检查地面上的油渍以及油尺上的油位。确认油底壳垫圈是否完好，是否有可见裂纹。</li>
<li>第二步 - 压力测试：如果怀疑有裂纹，需对油底壳进行压力测试以检查漏油。这涉及密封油底壳并施加气压，以识别任何薄弱点。确保压力在设定时间内保持稳定，以确认其完整性。</li>
<li>第三步 - 油液分析：如果怀疑油液污染，需取样进行分析。这可以揭示是否存在金属屑或其他污染物，表明内部磨损。这种分析可能需要将样本送到实验室进行详细检查。</li>
<li>第四步 - 试车监测：维修完成后进行试驾，监测油压和温度。使用油压表确保在不同条件下油压保持在25-60 PSI之间。在驾驶过程中观察是否有漏油迹象。</li>

                            </ol>
                        </div>

                        <!-- Repair Recommendations -->
                        <div class="main-content repair-recommendations">
                            <h2 id="repair-recommendations"><i class="fa fa-wrench"></i> 维修建议</h2>
                            <div class="recommendations-list">
                                
            <div class="recommendation-item priority-critical">
                <p>紧急优先 - 更换油底壳：鉴于漏油和潜在的发动机损坏，更换油底壳应优先处理。使用原厂零件P/N 31220280，预计零件费用约300-500元，人工费用约150-300元。安装时确保使用新垫圈以防止未来漏油。</p>
                <span class="priority">优先级: critical</span>
            </div>
            
            <div class="recommendation-item priority-high">
                <p>重点维修 - 放油螺母检查与更换：检查放油螺母的状况，如磨损或损坏则需更换。新螺母费用约为10-30元，人工费用不超过50元。避免此处漏油对保持安全油位至关重要。</p>
                <span class="priority">优先级: high</span>
            </div>
            
            <div class="recommendation-item priority-medium">
                <p>中等优先 - 定期更换机油：实施严格的更换机油计划，理想情况下每5000-7500公里更换一次，使用优质合成机油。这将有助于防止污染并延长发动机寿命。在每次更换时监测机油状况也是推荐的。</p>
                <span class="priority">优先级: medium</span>
            </div>
            
                            </div>
                        </div>

                        <!-- Preventive Tips -->
                        <div class="main-content preventive-tips">
                            <h2 id="preventive-tips"><i class="fa fa-shield"></i> 预防提示</h2>
                            <ul>
                                <li>定期视觉检查：定期检查油底壳及周围区域，及时发现漏油的早期迹象。这种主动维护可以节省后续昂贵的维修费用。</li>
<li>机油质量监测：记录机油的质量和更换间隔。使用高品质合成机油可以降低发动机磨损和漏油的风险。考虑使用油液分析服务获取更详细的信息。</li>
<li>保持放油螺母完好：定期检查放油螺母的紧固程度和磨损迹象。确保其保持牢固可以防止漏油导致油位过低。</li>

                            </ul>
                        </div>
                    </div>

                    <!-- Sidebar Column -->
                    <div class="col-lg-4 col-md-4">
                        <!-- GeekOBD APP Promotion -->
                        <div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: white; font-size: 20px; text-align: center;"><i class="fa fa-mobile"></i> 诊断问题</h4>
                            <p style="margin-bottom: 15px; color: rgba(255,255,255,0.9); font-size: 14px; text-align: center;">使用GeekOBD APP进行专业诊断！</p>
                            <div style="margin-bottom: 20px;">
                                <ul style="color: rgba(255,255,255,0.9); font-size: 14px; margin: 0; padding-left: 0; list-style: none;">
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>实时数据监控</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>高级诊断功能</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>逐步维修指导</li>
                                    <li style="margin-bottom: 8px;"><i class="fa fa-check" style="margin-right: 8px; color: #81c784;"></i>专业级分析</li>
                                </ul>
                            </div>
                            <div class="button-container" style="display: flex; gap: 10px; flex-direction: column;">
                                <a href="../app.html" style="background: rgba(255,255,255,0.2); color: white; padding: 15px 20px; text-decoration: none; border-radius: 25px; text-align: center; font-size: 16px; font-weight: 600; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s;">
                                    下载GeekOBD APP
                                </a>
                                <a href="../hardware.html" style="background: rgba(255,255,255,0.2); color: white; padding: 15px 20px; text-decoration: none; border-radius: 25px; text-align: center; font-size: 16px; font-weight: 600; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s;">
                                    获取MOBD适配器
                                </a>
                            </div>
                        </div>

                        <!-- Quick Navigation -->
                        <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-compass"></i> 快速导航</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <a href="#user-problem-summary" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-user"></i> 车主问题描述
                                </a>
                                <a href="#problem-overview" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-cogs"></i> 专业技术分析
                                </a>
                                <a href="#causes" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-search"></i> 可能原因
                                </a>
                                <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-stethoscope"></i> 诊断步骤
                                </a>
                                <a href="#repair-recommendations" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-wrench"></i> 维修建议
                                </a>
                                <a href="#preventive-tips" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    <i class="fa fa-shield"></i> 预防提示
                                </a>
                            </div>
                        </div>

                        <!-- Diagnostic Resources -->
                        <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-book"></i> 诊断资源</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <a href="../obd-diagnostic-guide.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    OBD诊断指南
                                </a>
                                <a href="../vehicle-compatibility.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    车辆兼容性
                                </a>
                                <a href="../fuel-efficiency-monitoring.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    燃油效率提示
                                </a>
                                <a href="../support.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    技术支持
                                </a>
                                <a href="../blog.html" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
                                    最新文章
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </main>
        <!-- Content End -->
    </div>

    <!-- JavaScript -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.js"></script>
    <script src="../js/superfish.js"></script>
    <script src="../js/custom.js"></script>
</body>
</html>